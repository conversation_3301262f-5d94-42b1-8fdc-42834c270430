import { base } from 'viem/chains';

import { ProjectConfig } from '@/providers/project-config-provider';

export const config: ProjectConfig = {
  walletConnectProjectId: '9df96a2808a7aac07d979cb9102f6359',
  chain: base,
  name: '<PERSON>in<PERSON>',
  vestedToken: {
    name: '<PERSON><PERSON><PERSON>',
    symbol: 'COIIN',
    decimals: 18,
    address: '******************************************',
    logo: 'https://assets-global.website-files.com/657250beec68dc870dad3faa/6580b6cfe894bde4c77db1f2_Favicon.png',
  },
  collectedToken: {
    symbol: 'USDC',
    decimals: 6,
    address: '******************************************',
  },
  presaleVersion: 'v5',
  presaleAddress: '******************************************',
  company: {
    name: '<PERSON><PERSON><PERSON>',
    website: 'https://www.raiinmaker.com/',
    logo: '/logo/raiinmaker.png',
  },
  colors: {
    background: '#000',
    text: '#fff',
    accent: '#ce2eef',
    header: {
      background: '#000',
    },
    footer: {
      background: '#000',
      text: '#fff',
    },
  },
};
